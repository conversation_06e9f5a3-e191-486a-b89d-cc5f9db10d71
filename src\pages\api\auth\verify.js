import { verifyAdminToken } from '../../../middleware/auth';

export default function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  // Use the middleware to verify the token
  verifyAdminToken(req, res, () => {
    // If we reach here, the token is valid
    return res.status(200).json({
      success: true,
      message: 'Token is valid',
      user: req.user
    });
  });
}
