// app/page.jsx
import Head from "next/head";
import Hero from "../hero/page";
import Features from "../Features/page";
import ProductShowcase from "../ProductShowcase/page";
import ContactForm from "../ContactForm/page";
import Footer from "../Footer/page";

export default function HomePage() {
  return (
    <>
      <Head>
        <title>Laptop Rentals Dubai | Affordable Laptop Hire Services</title>
        <meta
          name="description"
          content="Rent laptops in Dubai at affordable prices. Get fast delivery, 24/7 support, and top-notch devices for business or personal use."
        />
        <meta name="keywords" content="laptop rentals dubai, rent laptops uae, affordable laptop hire" />
        <meta name="author" content="Laptop Rentals" />
        <meta property="og:title" content="Laptop Rentals Dubai | Affordable Laptop Hire Services" />
        <meta property="og:description" content="Get reliable laptops on rent in Dubai for business or personal needs. Quick service, quality tech." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com" />
        <meta property="og:image" content="https://yourdomain.com/og-image.jpg" />
      </Head>

      <main className="min-h-screen bg-gray-50 text-gray-800">
        <Hero />
        <Features />
        <ProductShowcase />
        <ContactForm />
        <Footer />
      </main>
    </>
  );
}
