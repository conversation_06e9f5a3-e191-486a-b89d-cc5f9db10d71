import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Laptop2, User2, <PERSON>Chart, TrendingUp, Calendar, Settings, Bell, Search, Filter, Download, Eye, Edit, Trash2, MoreVertical, DollarSign, ArrowUpRight, ArrowDownRight, Clock, CheckCircle, AlertCircle, Users, Target, Zap } from "lucide-react";

export default function AdminPage() {
  const recentRentals = [
    { id: 1, customer: "Ahmed Al-Rashid", laptop: "MacBook Pro M3", duration: "7 days", status: "Active", amount: "AED 350", avatar: "AA", startDate: "2024-01-15" },
    { id: 2, customer: "<PERSON>", laptop: "Dell XPS 13", duration: "14 days", status: "Pending", amount: "AED 280", avatar: "SJ", startDate: "2024-01-18" },
    { id: 3, customer: "Mohammed Hassan", laptop: "ThinkPad X1", duration: "30 days", status: "Completed", amount: "AED 600", avatar: "MH", startDate: "2024-01-10" },
    { id: 4, customer: "<PERSON> Chen", laptop: "Surface Laptop", duration: "3 days", status: "Active", amount: "AED 150", avatar: "LC", startDate: "2024-01-20" },
  ];

  const Laptops = [
    { id: 1, name: "MacBook Pro M3", brand: "Apple", quantity: 10, price: "AED 180/day", image: "/api/placeholder/300/200" },
    { id: 2, name: "Dell XPS 13", brand: "Dell", quantity: 5, price: "AED 150/day", image: "/api/placeholder/300/200" },
    { id: 3, name: "ThinkPad X1", brand: "Lenovo", quantity: 8, price: "AED 120/day", image: "/api/placeholder/300/200" },
    { id: 4, name: "ROG Strix G16", brand: "ASUS", quantity: 3, price: "AED 250/day", image: "/api/placeholder/300/200" },
  ];

  const leads = [
    { name: "Tech Corp", email: "<EMAIL>", phone: "+971-50-123-4567", interest: "Bulk Rental", priority: "High", value: "AED 5,000", avatar: "TC" },
    { name: "Event Solutions", email: "<EMAIL>", phone: "+971-55-987-6543", interest: "Gaming Laptops", priority: "Medium", value: "AED 2,500", avatar: "ES" },
    { name: "Training Center", email: "<EMAIL>", phone: "+971-52-456-7890", interest: "Business Laptops", priority: "Low", value: "AED 1,200", avatar: "TR" },
  ];

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      {/* Enhanced Header with Gradient Background */}
      <div className="bg-white border-b border-slate-200 shadow-sm">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <LineChart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                  Dashboard Overview
                </h1>
                <p className="text-slate-600 text-sm mt-1">Monitor your rental business performance and manage operations</p>
              </div>
            </div>
            <div className="flex items-center gap-3 mt-6 lg:mt-0">
              <div className="flex items-center bg-slate-100 rounded-lg px-3 py-2">
                <Search className="w-4 h-4 text-slate-400 mr-2" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="bg-transparent text-sm text-slate-600 placeholder-slate-400 border-none outline-none w-32"
                />
              </div>
              <Button variant="outline" size="sm" className="text-slate-600 border-slate-300 hover:bg-slate-50">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="text-slate-600 border-slate-300 hover:bg-slate-50">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg">
                <Plus className="w-4 h-4 mr-2" />
                New Rental
              </Button>
              <Button variant="outline" size="sm" className="w-10 h-10 p-0 border-slate-300">
                <Bell className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Enhanced KPI Cards */}
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-700 text-sm font-medium mb-1">Total Rentals</p>
                  <p className="text-3xl font-bold text-blue-900 mb-2">24</p>
                  <div className="flex items-center">
                    <ArrowUpRight className="w-4 h-4 text-emerald-600 mr-1" />
                    <span className="text-emerald-600 text-sm font-semibold">+12%</span>
                    <span className="text-blue-600 text-sm ml-1">vs last month</span>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Laptop2 className="w-7 h-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-emerald-50 to-emerald-100/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-emerald-700 text-sm font-medium mb-1">Active Leads</p>
                  <p className="text-3xl font-bold text-emerald-900 mb-2">12</p>
                  <div className="flex items-center">
                    <Zap className="w-4 h-4 text-emerald-600 mr-1" />
                    <span className="text-emerald-600 text-sm font-semibold">+5</span>
                    <span className="text-emerald-600 text-sm ml-1">new this week</span>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Users className="w-7 h-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-violet-50 to-violet-100/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-violet-700 text-sm font-medium mb-1">Monthly Revenue</p>
                  <p className="text-3xl font-bold text-violet-900 mb-2">AED 8,500</p>
                  <div className="flex items-center">
                    <TrendingUp className="w-4 h-4 text-emerald-600 mr-1" />
                    <span className="text-emerald-600 text-sm font-semibold">+18%</span>
                    <span className="text-violet-600 text-sm ml-1">growth</span>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-violet-500 to-violet-600 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="w-7 h-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-amber-50 to-amber-100/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-amber-700 text-sm font-medium mb-1">Pending Returns</p>
                  <p className="text-3xl font-bold text-amber-900 mb-2">8</p>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-amber-600 mr-1" />
                    <span className="text-amber-600 text-sm font-semibold">Due</span>
                    <span className="text-amber-600 text-sm ml-1">this week</span>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-amber-500 to-amber-600 p-4 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <Calendar className="w-7 h-7 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Enhanced Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Enhanced Recent Rentals Table */}
          <div className="lg:col-span-2">
            <Card className="border-0 bg-white shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="p-6 border-b border-slate-100 bg-gradient-to-r from-slate-50 to-blue-50/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                        <Laptop2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-slate-900">Recent Rentals</h2>
                        <p className="text-sm text-slate-600">Latest rental activities</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="text-slate-600 border-slate-300 hover:bg-blue-50">
                      View All
                    </Button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-slate-50 to-slate-100">
                      <tr>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Customer</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Device</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Duration</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Status</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Amount</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-100">
                      {recentRentals.map((rental) => (
                        <tr key={rental.id} className="hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/20 transition-all duration-200">
                          <td className="py-5 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                {rental.avatar}
                              </div>
                              <div>
                                <div className="text-sm font-semibold text-slate-900">{rental.customer}</div>
                                <div className="text-xs text-slate-500">Started: {rental.startDate}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm font-medium text-slate-900">{rental.laptop}</div>
                            <div className="text-xs text-slate-500">Premium Device</div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm text-slate-700 font-medium">{rental.duration}</div>
                          </td>
                          <td className="py-5 px-6">
                            <span className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${
                              rental.status === 'Active' ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300' :
                              rental.status === 'Pending' ? 'bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800 border border-amber-300' :
                              'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-800 border border-slate-300'
                            }`}>
                              {rental.status === 'Active' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {rental.status === 'Pending' && <Clock className="w-3 h-3 mr-1" />}
                              {rental.status === 'Completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {rental.status}
                            </span>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm font-bold text-slate-900">{rental.amount}</div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600">
                                <Eye className="w- h-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-emerald-100 hover:text-emerald-600">
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-slate-100">
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Laptops Available Table */}
            <Card className="border-0 bg-white shadow-xl hover:shadow-2xl transition-all duration-300 mt-8">
              <CardContent className="p-0">
                <div className="p-6 border-b border-slate-100 bg-gradient-to-r from-indigo-50 to-purple-50/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                        <Laptop2 className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-slate-900">Available Laptops</h2>
                        <p className="text-sm text-slate-600">Current inventory status</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" className="text-slate-600 border-slate-300 hover:bg-indigo-50">
                        <Filter className="w-4 h-4 mr-2" />
                        Filter
                      </Button>
                      <Button variant="outline" size="sm" className="text-indigo-600 border-indigo-300 hover:bg-indigo-50">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Laptop
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-slate-50 to-slate-100">
                      <tr>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Device</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Model</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Specifications</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Status</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Daily Rate</th>
                        <th className="text-left py-4 px-6 text-xs font-semibold text-slate-600 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-100">
                      {[
                        { id: 1, brand: "Apple", model: "MacBook Pro 16\"", specs: "M3 Pro, 18GB RAM, 512GB SSD", status: "Available", rate: "AED 180", condition: "Excellent", serialNo: "MBP001" },
                        { id: 2, brand: "Dell", model: "XPS 13", specs: "Intel i7, 16GB RAM, 512GB SSD", status: "Rented", rate: "AED 120", condition: "Good", serialNo: "XPS002" },
                        { id: 3, brand: "Lenovo", model: "ThinkPad X1 Carbon", specs: "Intel i7, 16GB RAM, 1TB SSD", status: "Available", rate: "AED 140", condition: "Excellent", serialNo: "TP003" },
                        { id: 4, brand: "HP", model: "Spectre x360", specs: "Intel i5, 8GB RAM, 256GB SSD", status: "Maintenance", rate: "AED 100", condition: "Fair", serialNo: "HP004" },
                        { id: 5, brand: "ASUS", model: "ROG Strix G16", specs: "Intel i9, 32GB RAM, RTX 4070", status: "Available", rate: "AED 250", condition: "Excellent", serialNo: "ROG005" },
                        { id: 6, brand: "Microsoft", model: "Surface Laptop 5", specs: "Intel i7, 16GB RAM, 512GB SSD", status: "Available", rate: "AED 130", condition: "Good", serialNo: "SF006" }
                      ].map((laptop) => (
                        <tr key={laptop.id} className="hover:bg-gradient-to-r hover:from-indigo-50/30 hover:to-purple-50/20 transition-all duration-200">
                          <td className="py-5 px-6">
                            <div className="flex items-center space-x-3">
                              <div className="w-12 h-12 bg-gradient-to-br from-slate-100 to-slate-200 rounded-lg flex items-center justify-center">
                                <Laptop2 className="w-6 h-6 text-slate-600" />
                              </div>
                              <div>
                                <div className="text-sm font-semibold text-slate-900">{laptop.brand}</div>
                                <div className="text-xs text-slate-500">#{laptop.serialNo}</div>
                              </div>
                            </div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm font-medium text-slate-900">{laptop.model}</div>
                            <div className="text-xs text-slate-500">{laptop.condition} condition</div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm text-slate-700">{laptop.specs}</div>
                          </td>
                          <td className="py-5 px-6">
                            <span className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${
                              laptop.status === 'Available' ? 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 border border-emerald-300' :
                              laptop.status === 'Rented' ? 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300' :
                              'bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800 border border-amber-300'
                            }`}>
                              {laptop.status === 'Available' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {laptop.status === 'Rented' && <User2 className="w-3 h-3 mr-1" />}
                              {laptop.status === 'Maintenance' && <AlertCircle className="w-3 h-3 mr-1" />}
                              {laptop.status}
                            </span>
                          </td>
                          <td className="py-5 px-6">
                            <div className="text-sm font-bold text-slate-900">{laptop.rate}</div>
                            <div className="text-xs text-slate-500">per day</div>
                          </td>
                          <td className="py-5 px-6">
                            <div className="flex items-center gap-1">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-indigo-100 hover:text-indigo-600">
                                <Eye className="w-4 h-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-emerald-100 hover:text-emerald-600">
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-slate-100">
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Sidebar */}
          <div className="space-y-8">
            {/* Enhanced Leads Section */}
            <Card className="border-0 bg-white shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="p-6 border-b border-slate-100 bg-gradient-to-r from-emerald-50 to-green-50/30">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-500 rounded-lg flex items-center justify-center">
                        <Target className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-slate-900">Recent Leads</h3>
                        <p className="text-sm text-slate-600">Hot prospects</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="text-emerald-600 border-emerald-300 hover:bg-emerald-50">
                      <Plus className="w-4 h-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  {leads.map((lead, index) => (
                    <div key={index} className="border border-slate-200 rounded-xl p-4 hover:shadow-lg transition-all duration-200 bg-gradient-to-r from-white to-slate-50/30">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-700 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                            {lead.avatar}
                          </div>
                          <div>
                            <h4 className="font-semibold text-slate-900 text-sm">{lead.name}</h4>
                            <p className="text-xs text-slate-500">{lead.value}</p>
                          </div>
                        </div>
                        <span className={`px-3 py-1 text-xs font-semibold rounded-full shadow-sm ${
                          lead.priority === 'High' ? 'bg-gradient-to-r from-red-100 to-red-200 text-red-700 border border-red-300' :
                          lead.priority === 'Medium' ? 'bg-gradient-to-r from-amber-100 to-amber-200 text-amber-700 border border-amber-300' :
                          'bg-gradient-to-r from-slate-100 to-slate-200 text-slate-700 border border-slate-300'
                        }`}>
                          {lead.priority}
                        </span>
                      </div>
                      <div className="space-y-1 mb-3">
                        <p className="text-xs text-slate-600 flex items-center">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                          {lead.email}
                        </p>
                        <p className="text-xs text-slate-600 flex items-center">
                          <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                          {lead.phone}
                        </p>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-xs font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded-full">{lead.interest}</p>
                        <Button variant="ghost" size="sm" className="h-6 text-xs hover:bg-blue-50 hover:text-blue-600">
                          Contact
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced Performance Metrics */}
            <Card className="border-0 bg-white shadow-xl hover:shadow-2xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="p-6 border-b border-slate-100 bg-gradient-to-r from-violet-50 to-purple-50/30">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <LineChart className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-slate-900">Key Metrics</h3>
                      <p className="text-sm text-slate-600">Performance indicators</p>
                    </div>
                  </div>
                </div>

                <div className="p-6 space-y-6">
                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-emerald-50 to-green-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                        <Target className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-medium text-slate-700">Conversion Rate</span>
                    </div>
                    <span className="text-lg font-bold text-emerald-600">68%</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Calendar className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-medium text-slate-700">Avg. Rental Period</span>
                    </div>
                    <span className="text-lg font-bold text-blue-600">12 days</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-bold">★</span>
                      </div>
                      <span className="text-sm font-medium text-slate-700">Customer Rating</span>
                    </div>
                    <span className="text-lg font-bold text-amber-600">4.8/5</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-violet-500 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-sm font-medium text-slate-700">Repeat Customers</span>
                    </div>
                    <span className="text-lg font-bold text-violet-600">45%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  );
}
