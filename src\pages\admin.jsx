import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Laptop2, User2, <PERSON>Chart, TrendingUp, Calendar, Settings, Bell, Search, Filter, Download, Eye, Edit, Trash2, MoreVertical, DollarSign } from "lucide-react";

export default function AdminPage() {
  const recentRentals = [
    { id: 1, customer: "Ahmed Al-Rashid", laptop: "MacBook Pro M3", duration: "7 days", status: "Active", amount: "AED 350" },
    { id: 2, customer: "<PERSON>", laptop: "Dell XPS 13", duration: "14 days", status: "Pending", amount: "AED 280" },
    { id: 3, customer: "<PERSON>", laptop: "ThinkPad X1", duration: "30 days", status: "Completed", amount: "AED 600" },
    { id: 4, customer: "<PERSON>", laptop: "Surface Laptop", duration: "3 days", status: "Active", amount: "AED 150" },
  ];

  const leads = [
    { name: "Tech Corp", email: "<EMAIL>", phone: "+971-50-123-4567", interest: "Bulk Rental", priority: "High" },
    { name: "Event Solutions", email: "<EMAIL>", phone: "+971-55-987-6543", interest: "Gaming Laptops", priority: "Medium" },
    { name: "Training Center", email: "<EMAIL>", phone: "+971-52-456-7890", interest: "Business Laptops", priority: "Low" },
  ];

  return (
    <main className="p-6 bg-slate-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
        <div>
          <h1 className="text-2xl font-semibold text-slate-900 mb-1">
            Dashboard Overview
          </h1>
          <p className="text-slate-600 text-sm">Monitor your rental business performance and manage operations</p>
        </div>
        <div className="flex items-center gap-3 mt-4 lg:mt-0">
          <Button variant="outline" size="sm" className="text-slate-600 border-slate-300">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm" className="text-slate-600 border-slate-300">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button className="bg-slate-900 hover:bg-slate-800 text-white">
            <Plus className="w-4 h-4 mr-2" />
            New Rental
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="border-slate-200 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Rentals</p>
                <p className="text-2xl font-semibold text-slate-900 mt-1">24</p>
                <div className="flex items-center mt-2">
                  <span className="text-emerald-600 text-sm font-medium">+12%</span>
                  <span className="text-slate-500 text-sm ml-1">vs last month</span>
                </div>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <Laptop2 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-slate-200 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Active Leads</p>
                <p className="text-2xl font-semibold text-slate-900 mt-1">12</p>
                <div className="flex items-center mt-2">
                  <span className="text-emerald-600 text-sm font-medium">+5</span>
                  <span className="text-slate-500 text-sm ml-1">new this week</span>
                </div>
              </div>
              <div className="bg-emerald-50 p-3 rounded-lg">
                <User2 className="w-6 h-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-slate-200 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Monthly Revenue</p>
                <p className="text-2xl font-semibold text-slate-900 mt-1">AED 8,500</p>
                <div className="flex items-center mt-2">
                  <span className="text-emerald-600 text-sm font-medium">+18%</span>
                  <span className="text-slate-500 text-sm ml-1">growth</span>
                </div>
              </div>
              <div className="bg-violet-50 p-3 rounded-lg">
                <DollarSign className="w-6 h-6 text-violet-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-slate-200 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Pending Returns</p>
                <p className="text-2xl font-semibold text-slate-900 mt-1">8</p>
                <div className="flex items-center mt-2">
                  <span className="text-amber-600 text-sm font-medium">Due</span>
                  <span className="text-slate-500 text-sm ml-1">this week</span>
                </div>
              </div>
              <div className="bg-amber-50 p-3 rounded-lg">
                <Calendar className="w-6 h-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Rentals Table */}
        <div className="lg:col-span-2">
          <Card className="border-slate-200 bg-white shadow-sm">
            <CardContent className="p-0">
              <div className="p-6 border-b border-slate-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-slate-900">Recent Rentals</h2>
                  <Button variant="outline" size="sm" className="text-slate-600 border-slate-300">
                    View All
                  </Button>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-slate-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Customer</th>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Device</th>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Duration</th>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Amount</th>
                      <th className="text-left py-3 px-6 text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {recentRentals.map((rental) => (
                      <tr key={rental.id} className="hover:bg-slate-50">
                        <td className="py-4 px-6">
                          <div className="text-sm font-medium text-slate-900">{rental.customer}</div>
                        </td>
                        <td className="py-4 px-6 text-sm text-slate-600">{rental.laptop}</td>
                        <td className="py-4 px-6 text-sm text-slate-600">{rental.duration}</td>
                        <td className="py-4 px-6">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            rental.status === 'Active' ? 'bg-emerald-100 text-emerald-800' :
                            rental.status === 'Pending' ? 'bg-amber-100 text-amber-800' :
                            'bg-slate-100 text-slate-800'
                          }`}>
                            {rental.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-sm font-medium text-slate-900">{rental.amount}</td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Leads */}
          <Card className="border-slate-200 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-slate-900">Recent Leads</h3>
                <Button variant="outline" size="sm" className="text-slate-600 border-slate-300">
                  <Plus className="w-4 h-4 mr-1" />
                  Add
                </Button>
              </div>
              
              <div className="space-y-4">
                {leads.map((lead, index) => (
                  <div key={index} className="border border-slate-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-slate-900 text-sm">{lead.name}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        lead.priority === 'High' ? 'bg-red-100 text-red-700' :
                        lead.priority === 'Medium' ? 'bg-amber-100 text-amber-700' :
                        'bg-slate-100 text-slate-700'
                      }`}>
                        {lead.priority}
                      </span>
                    </div>
                    <p className="text-xs text-slate-600 mb-1">{lead.email}</p>
                    <p className="text-xs text-slate-600 mb-2">{lead.phone}</p>
                    <p className="text-xs font-medium text-blue-600">{lead.interest}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card className="border-slate-200 bg-white shadow-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Key Metrics</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-600">Conversion Rate</span>
                  <span className="text-sm font-semibold text-slate-900">68%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-600">Avg. Rental Period</span>
                  <span className="text-sm font-semibold text-slate-900">12 days</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-600">Customer Rating</span>
                  <span className="text-sm font-semibold text-slate-900">4.8/5</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-slate-600">Repeat Customers</span>
                  <span className="text-sm font-semibold text-slate-900">45%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
