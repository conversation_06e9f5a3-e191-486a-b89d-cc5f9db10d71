import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // Get token from cookie or Authorization header
    let token = null;
    
    // Check for token in cookies first
    if (req.cookies && req.cookies['auth-token']) {
      token = req.cookies['auth-token'];
    }
    
    // Check for token in Authorization header as fallback
    if (!token && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    // Verify token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'No authentication token found'
      });
    }

    // Verify token is valid
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    // Clear the HTTP-only cookie
    res.setHeader('Set-Cookie', [
      `auth-token=; HttpOnly; Path=/; Max-Age=0; SameSite=Strict${
        process.env.NODE_ENV === 'production' ? '; Secure' : ''
      }`
    ]);

    // In a production environment, you might want to:
    // 1. Add the token to a blacklist/revoked tokens list
    // 2. Store revoked tokens in Redis or database
    // 3. Log the logout event for security auditing

    return res.status(200).json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// Optional: Helper function to add token to blacklist (for production use)
export function addToBlacklist(token) {
  // In production, implement token blacklisting:
  // - Store in Redis with expiration
  // - Store in database
  // - Use in-memory cache for smaller applications
  console.log('Token blacklisted:', token.substring(0, 20) + '...');
}
