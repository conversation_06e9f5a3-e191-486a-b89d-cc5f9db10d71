import { withAdminAuth } from '../../../middleware/auth';

async function dashboardHandler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // This is a protected route - req.user is available from middleware
    const { user } = req;

    // Mock dashboard data - in real app, fetch from database
    const dashboardData = {
      stats: {
        totalRentals: 24,
        activeLeads: 12,
        monthlyRevenue: 8500,
        pendingReturns: 8
      },
      recentRentals: [
        { id: 1, customer: "<PERSON>", laptop: "MacBook Pro M3", duration: "7 days", status: "Active", amount: "AED 350" },
        { id: 2, customer: "<PERSON>", laptop: "Dell XPS 13", duration: "14 days", status: "Pending", amount: "AED 280" },
        { id: 3, customer: "Mohammed Hassan", laptop: "ThinkPad X1", duration: "30 days", status: "Completed", amount: "AED 600" },
        { id: 4, customer: "<PERSON>", laptop: "Surface Laptop", duration: "3 days", status: "Active", amount: "AED 150" },
      ],
      leads: [
        { name: "Tech Corp", email: "<EMAIL>", phone: "+971-50-123-4567", interest: "Bulk Rental", priority: "High" },
        { name: "Event Solutions", email: "<EMAIL>", phone: "+971-55-987-6543", interest: "Gaming Laptops", priority: "Medium" },
        { name: "Training Center", email: "<EMAIL>", phone: "+971-52-456-7890", interest: "Business Laptops", priority: "Low" },
      ],
      availableLaptops: [
        { id: 1, brand: "Apple", model: "MacBook Pro 16\"", specs: "M3 Pro, 18GB RAM, 512GB SSD", status: "Available", rate: "AED 180" },
        { id: 2, brand: "Dell", model: "XPS 13", specs: "Intel i7, 16GB RAM, 512GB SSD", status: "Rented", rate: "AED 120" },
        { id: 3, brand: "Lenovo", model: "ThinkPad X1 Carbon", specs: "Intel i7, 16GB RAM, 1TB SSD", status: "Available", rate: "AED 140" },
      ]
    };

    return res.status(200).json({
      success: true,
      message: 'Dashboard data retrieved successfully',
      data: dashboardData,
      user: {
        name: user.name,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// Export the protected handler
export default withAdminAuth(dashboardHandler);
