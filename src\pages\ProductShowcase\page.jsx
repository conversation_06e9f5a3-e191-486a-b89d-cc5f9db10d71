import React, { useState } from 'react'

const ProductShowcase = () => {
  const [activeCategory, setActiveCategory] = useState('business')

  const categories = [
    { id: 'business', name: 'Business', icon: '💼' },
    { id: 'gaming', name: 'Gaming', icon: '🎮' },
    { id: 'creative', name: 'Creative', icon: '🎨' },
    { id: 'student', name: 'Student', icon: '🎓' }
  ]

  const laptops = {
    business: [
      {
        id: 1,
        name: 'MacBook Pro 16"',
        brand: 'Apple',
        specs: ['M3 Pro Chip', '18GB RAM', '512GB SSD', '16" Liquid Retina XDR'],
        price: 'AED 180/day',
        image: '/api/placeholder/300/200',
        popular: true
      },
      {
        id: 2,
        name: 'ThinkPad X1 Carbon',
        brand: 'Lenovo',
        specs: ['Intel i7-13th Gen', '16GB RAM', '1TB SSD', '14" WQXGA'],
        price: 'AED 120/day',
        image: '/api/placeholder/300/200'
      },
      {
        id: 3,
        name: 'Surface Laptop 5',
        brand: 'Microsoft',
        specs: ['Intel i7-12th Gen', '16GB RAM', '512GB SSD', '13.5" PixelSense'],
        price: 'AED 100/day',
        image: '/api/placeholder/300/200'
      }
    ],
    gaming: [
      {
        id: 4,
        name: 'ROG Strix G16',
        brand: 'ASUS',
        specs: ['Intel i9-13th Gen', '32GB RAM', '1TB SSD', 'RTX 4070'],
        price: 'AED 250/day',
        image: '/api/placeholder/300/200',
        popular: true
      },
      {
        id: 5,
        name: 'Legion 7i',
        brand: 'Lenovo',
        specs: ['Intel i7-13th Gen', '16GB RAM', '1TB SSD', 'RTX 4060'],
        price: 'AED 200/day',
        image: '/api/placeholder/300/200'
      },
      {
        id: 6,
        name: 'Alienware m15 R7',
        brand: 'Dell',
        specs: ['Intel i7-12th Gen', '16GB RAM', '512GB SSD', 'RTX 4050'],
        price: 'AED 220/day',
        image: '/api/placeholder/300/200'
      }
    ],
    creative: [
      {
        id: 7,
        name: 'MacBook Pro 14"',
        brand: 'Apple',
        specs: ['M3 Max Chip', '36GB RAM', '1TB SSD', '14" Liquid Retina XDR'],
        price: 'AED 200/day',
        image: '/api/placeholder/300/200',
        popular: true
      },
      {
        id: 8,
        name: 'ZBook Studio G9',
        brand: 'HP',
        specs: ['Intel i9-12th Gen', '32GB RAM', '1TB SSD', 'RTX A2000'],
        price: 'AED 180/day',
        image: '/api/placeholder/300/200'
      },
      {
        id: 9,
        name: 'Precision 5570',
        brand: 'Dell',
        specs: ['Intel i7-12th Gen', '32GB RAM', '1TB SSD', 'RTX A1000'],
        price: 'AED 160/day',
        image: '/api/placeholder/300/200'
      }
    ],
    student: [
      {
        id: 10,
        name: 'MacBook Air M2',
        brand: 'Apple',
        specs: ['M2 Chip', '8GB RAM', '256GB SSD', '13.6" Liquid Retina'],
        price: 'AED 80/day',
        image: '/api/placeholder/300/200',
        popular: true
      },
      {
        id: 11,
        name: 'IdeaPad 5 Pro',
        brand: 'Lenovo',
        specs: ['AMD Ryzen 7', '16GB RAM', '512GB SSD', '14" 2.8K OLED'],
        price: 'AED 70/day',
        image: '/api/placeholder/300/200'
      },
      {
        id: 12,
        name: 'Inspiron 15 3000',
        brand: 'Dell',
        specs: ['Intel i5-12th Gen', '8GB RAM', '256GB SSD', '15.6" FHD'],
        price: 'AED 50/day',
        image: '/api/placeholder/300/200'
      }
    ]
  }

  return (
    <section id="products" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Laptop Collection
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose from our extensive range of premium laptops, perfect for every need and budget.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-full font-semibold transition-all duration-200 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="text-lg">{category.icon}</span>
              <span>{category.name}</span>
            </button>
          ))}
        </div>

        {/* Laptop Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {laptops[activeCategory].map((laptop) => (
            <div
              key={laptop.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 overflow-hidden group"
            >
              {/* Image */}
              <div className="relative">
                <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-sm font-medium">{laptop.brand}</p>
                  </div>
                </div>
                {laptop.popular && (
                  <div className="absolute top-4 right-4 bg-yellow-500 text-black text-xs font-bold px-3 py-1 rounded-full">
                    Popular
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-xl font-semibold text-gray-900">{laptop.name}</h3>
                  <span className="text-lg font-bold text-blue-600">{laptop.price}</span>
                </div>

                <p className="text-gray-600 mb-4">{laptop.brand}</p>

                {/* Specs */}
                <div className="space-y-2 mb-6">
                  {laptop.specs.map((spec, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-600">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                      {spec}
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 rounded-lg transition-colors duration-200 group-hover:bg-blue-700">
                  Rent Now
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Can't Find What You're Looking For?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We have an extensive inventory beyond what's shown here. Contact us for custom requirements or bulk orders.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200">
                Request Custom Quote
              </button>
              <button className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200">
                View All Models
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ProductShowcase