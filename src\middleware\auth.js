import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

/**
 * Middleware to verify JWT token and authenticate admin users
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
export function verifyAdminToken(req, res, next) {
  try {
    // Get token from cookie or Authorization header
    let token = null;
    
    // Check for token in cookies first (more secure)
    if (req.cookies && req.cookies['auth-token']) {
      token = req.cookies['auth-token'];
    }
    
    // Check for token in Authorization header as fallback
    if (!token && req.headers.authorization) {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    // If no token found, return unauthorized
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No authentication token provided.',
        code: 'NO_TOKEN'
      });
    }

    // Verify and decode the token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Check if token has expired
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Token has expired.',
        code: 'TOKEN_EXPIRED'
      });
    }

    // Check if user has admin role
    if (decoded.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.',
        code: 'INSUFFICIENT_PRIVILEGES'
      });
    }

    // Add user info to request object
    req.user = {
      id: decoded.id,
      email: decoded.email,
      name: decoded.name,
      role: decoded.role
    };

    // Continue to next middleware/route handler
    next();

  } catch (error) {
    console.error('Token verification error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token.',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Token has expired.',
        code: 'TOKEN_EXPIRED'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error during authentication.',
      code: 'AUTH_ERROR'
    });
  }
}

/**
 * Higher-order function to protect API routes
 * @param {Function} handler - The API route handler
 * @returns {Function} - Protected route handler
 */
export function withAdminAuth(handler) {
  return async (req, res) => {
    return new Promise((resolve, reject) => {
      verifyAdminToken(req, res, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve(handler(req, res));
        }
      });
    });
  };
}

/**
 * Client-side authentication check utility
 * @returns {Object} - Authentication status and user data
 */
export async function checkClientAuth() {
  try {
    const response = await fetch('/api/auth/verify', {
      method: 'GET',
      credentials: 'include'
    });
    
    if (response.ok) {
      const data = await response.json();
      return {
        isAuthenticated: true,
        user: data.user
      };
    } else {
      return {
        isAuthenticated: false,
        user: null
      };
    }
  } catch (error) {
    console.error('Auth check error:', error);
    return {
      isAuthenticated: false,
      user: null
    };
  }
}

// Note: React hooks should be in separate files for client-side use
// This function is kept here for reference but should be moved to a hooks file
